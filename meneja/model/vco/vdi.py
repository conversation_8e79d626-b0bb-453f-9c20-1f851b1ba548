# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import time
from typing import List

from mongoengine import Document, EmbeddedDocument, EmbeddedDocument<PERSON>ield, In<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
from mongoengine.errors import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from meneja.lib.enumeration import Availa<PERSON><PERSON><PERSON>, InstanceBehaviorEnum, RDPStatus, VDIPofileStatus


class SessionWireguardPeer(EmbeddedDocument):
    """WireGuard peer configuration"""

    public_key = StringField(required=True, help_text="Peer public key")
    allowed_ips = StringField(required=True, help_text="List of allowed IPs for this peer")
    endpoint = StringField(required=False, help_text="Connection endpoint for this peer")


class VMConfig(EmbeddedDocument):
    memory = IntField(min_value=256, required=True, help_text="Memory (MiB)", metadata={"example": 4096})
    vcpus = IntField(min_value=1, required=True, help_text="Number of virtual CPUs", metadata={"example": 2})
    bootdisk = IntField(min_value=10, required=True, help_text="Boot disk size (GiB)", metadata={"example": 60})
    vgpu_profile = StringField(required=False, help_text="Optional vGPU profile name", metadata={"example": ""})
    image_id = StringField(required=True, help_text="Base image ID", metadata={"example": "ubuntu-22.04"})
    # 👇 Keep this as a short token, not a whole cloud-init
    cloud_init_template = StringField(
        required=True, help_text="User-data template name", metadata={"example": "win11-default"}
    )


class SessionWireguardConfig(EmbeddedDocument):
    address = StringField(required=True, help_text="CIDR e.g. *********/24")
    peers = ListField(EmbeddedDocumentField(SessionWireguardPeer), required=False, help_text="Configured peers")
    mtu = IntField(default=1420, required=False, help_text="MTU (bytes)")


class VDIStandbyVM(EmbeddedDocument):
    """Embedded document for a standby VM entry tied to a VDI profile"""

    vm_id = IntField(required=True, help_text="Virtual machine ID")
    vm_name = StringField(required=True, help_text="Virtual machine name")
    status = StringField(
        choices=["standby", "in-use"],
        default="standby",
        help_text="Standby VM lifecycle status",
    )
    rdp_status = StringField(
        choices=RDPStatus.values(),
        default=RDPStatus.NOT_RUNNING.value,
        help_text="VM rdp status",
    )
    rdp_host = StringField(required=False, help_text="Optional RDP IP/hostname")  # optional IP/host for RDP
    created_at = IntField(default=time.time, readOnly=True, help_text="Creation time (epoch seconds)")
    updated_at = IntField(default=time.time, readOnly=True, help_text="Update time (epoch seconds)")
    session_id = StringField(default="", help_text="Session ID")


class VDIProfile(Document):
    """VDI Profile for a customer"""

    meta = {"collection": "vdi_profiles"}

    customer_id = StringField(required=True, help_text="Owner customer ID")
    vco = StringField(required=True, help_text="VCO ID where profile is hosted")
    profile_id = StringField(
        required=True, unique=True, readOnly=True, help_text="Unique profile identifier", export=True
    )
    name = StringField(required=True, help_text="Human-friendly profile name")
    vm_config = EmbeddedDocumentField(VMConfig, required=True, help_text="Default VM configuration")
    cloudspace_id = StringField(required=True, help_text="Target cloudspace ID (location:id)")
    subnet = StringField(required=True, default="", help_text="Cloudspace private network subnet")
    standby_pool_size = IntField(min_value=1, required=True, help_text="Number of standby VMs to keep ready")
    maximum_pool_size = IntField(min_value=0, default=0, required=True, help_text="Maximum number of VMs in the pool")
    instance_behavior = StringField(
        choices=InstanceBehaviorEnum.values(),
        required=True,
        help_text="Session behavior (SINGLE_USE or DEDICATED)",
    )
    recycle_time_seconds = IntField(
        min_value=300, max_value=86400 * 7, required=True, help_text="Auto-recycle time for instances (seconds)"
    )
    availability = StringField(
        choices=AvailabilityEnum.values(), required=True, help_text="Default availability behavior"
    )
    backup_policy_id = IntField(required=False, help_text="Optional backup policy ID")
    wireguard_id = StringField(
        required=False, default="", readOnly=True, help_text="Remote WireGuard interface ID", export=True
    )
    created_at = IntField(default=time.time, readOnly=True, help_text="Creation time (epoch seconds)", export=True)
    updated_at = IntField(default=time.time, readOnly=True, help_text="Last update time (epoch seconds)", export=True)
    granted_roles = ListField(StringField(), required=False, help_text="Granted role IDs for access control")
    service_account_id = StringField(required=False, help_text="Service account ID")
    vms = ListField(
        EmbeddedDocumentField(VDIStandbyVM), required=False, default=lambda: [], help_text="Standby VM pool entries"
    )
    status = StringField(
        choices=VDIPofileStatus.values(),
        default=VDIPofileStatus.CREATING.value,
        help_text="Profile status",
    )

    @classmethod
    def get_vm_status(cls, customer_id: str, profile_id: str, vm_id: int):
        """Get VM status"""
        try:
            profile = cls.objects.get(
                customer_id=customer_id, profile_id=profile_id, status__ne=VDIPofileStatus.DELETED.value
            )
            for vm in profile.vms:
                if int(vm.vm_id) == int(vm_id):
                    return {"status": vm.status, "rdp_status": vm.rdp_status}
            return None
        except DoesNotExist:
            return None

    @classmethod
    def get_by_id(cls, customer_id: str, profile_id: str) -> "VDIProfile":
        """Get profile by ID"""
        return cls.objects.get(customer_id=customer_id, profile_id=profile_id, status__ne=VDIPofileStatus.DELETED.value)

    @classmethod
    def count(cls, customer_id: str, name: str) -> int:
        """Count names"""
        return cls.objects(customer_id=customer_id, name__startswith=name).count()

    @classmethod
    def list_all(cls) -> List["VDIProfile"]:
        """List all profiles (admin only)"""
        return cls.objects()

    @classmethod
    def list(cls, customer_id: str) -> List["VDIProfile"]:
        """List available profiles for a customer"""
        return cls.objects(customer_id=customer_id, status__ne=VDIPofileStatus.DELETED.value)

    @classmethod
    def delete_profile(cls, customer_id: str, profile_id: str) -> bool:
        """Delete a VDI profile by its ID"""
        return cls.get_by_id(customer_id, profile_id).delete()

    @classmethod
    def get_roles(cls, customer_id: str, profile_id: str) -> List[str]:
        """Get roles associated with a VDI profile"""
        profile = cls.get_by_id(customer_id, profile_id)
        return profile.granted_roles

    @classmethod
    def get_role(cls, customer_id: str, profile_id: str, role_id: str) -> str:
        """Get a specific role associated with a VDI profile"""
        roles = cls.get_roles(customer_id, profile_id)
        for role in roles:
            if role.role_id == role_id:
                return role
        raise DoesNotExist(f"Role '{role_id}' not found for VDI Profile '{profile_id}' of customer '{customer_id}'")

    @classmethod
    def set_profile_status(cls, customer_id: str, profile_id: str, status: str) -> "VDIProfile":
        """Set profile status"""
        cls.objects(customer_id=customer_id, profile_id=profile_id).update(set__status=status)


class VDIUser(EmbeddedDocument):
    """Embedded document for a VDI user"""

    username = StringField(help_text="User name")
    email = StringField(help_text="User email")


class VDISession(Document):
    """VDI Session document"""

    meta = {
        "collection": "vdi_sessions",
        "indexes": [
            "customer_id",
            "profile_id",
            "status",
            "user.username",
            "user.email",
        ],
    }

    session_id = StringField(required=True, unique=True, readOnly=True, help_text="Unique session ID", export=True)
    customer_id = StringField(required=True, help_text="Owner customer ID")
    profile_id = StringField(required=True, help_text="Associated VDI profile ID")
    status = StringField(
        choices=RDPStatus.values(),
        help_text="Session status",
    )
    vm_id = IntField(required=False, default=None, help_text="Associated VM ID (if any)")
    wireguard_config = EmbeddedDocumentField(
        SessionWireguardConfig, required=False, help_text="Client WireGuard configuration"
    )
    rdp_target_ip = StringField(required=True, help_text="RDP target IP/hostname")
    rdp_target_port = IntField(required=False, default=3389, help_text="RDP target port")
    cloudspace_id = StringField(required=False, default=None, help_text="Associated cloudspace ID (if any)")
    created_at = IntField(default=time.time, readOnly=True, help_text="Creation time (epoch seconds)", export=True)
    updated_at = IntField(default=time.time, readOnly=True, help_text="Last update time (epoch seconds)", export=True)
    ended_at = IntField(required=False, help_text="End time (epoch seconds)", export=True)

    user = EmbeddedDocumentField(VDIUser, required=False, help_text="User information")

    @classmethod
    def list(cls, customer_id: str, profile_id: str, filters: dict = None):
        """List VDI profile sessions"""
        query = cls.objects(customer_id=customer_id, profile_id=profile_id)
        if filters:
            for field, value in filters.items():
                if value is None:
                    continue
                if field in ["status", "user.username", "user.email"]:
                    query = query(**{field: value})
                elif field == "after_created_at":
                    query = query(created_at__gte=value)
                elif field == "before_created_at":
                    query = query(created_at__lte=value)
        return query

    @classmethod
    def get_by_id(cls, customer_id: str, profile_id: str, session_id: str):
        """Get session by ID"""
        return cls.objects.get(customer_id=customer_id, profile_id=profile_id, session_id=session_id)

    @classmethod
    def delete_session(cls, customer_id: str, profile_id: str, session_id: str):
        """Delete session by ID"""
        return cls.objects(
            customer_id=customer_id,
            profile_id=profile_id,
            session_id=session_id,
        ).delete()

    @classmethod
    def get_session_status(cls, customer_id: str, profile_id: str, session_id: str):
        """Get session status by ID"""
        return cls.objects(customer_id=customer_id, profile_id=profile_id, session_id=session_id).only("status").first()

    @classmethod
    def list_all(cls) -> List["VDISession"]:
        """List all sessions (admin only)"""
        return cls.objects()
