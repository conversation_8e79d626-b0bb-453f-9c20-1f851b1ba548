# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>DUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@


import base64
import json
from dataclasses import dataclass, field
from typing import List, Optional

from meneja.lib.enumeration import AvailabilityEnum, Instance<PERSON><PERSON><PERSON>or<PERSON>num, R<PERSON><PERSON>tatus, Standby<PERSON>tatus, WgInterfaceType
from meneja.structs.dataclasses import BaseStruct

# --- Config Structs ---


@dataclass
class VMStatusStruct(BaseStruct):
    rdp_status: str = field(metadata=dict(help_text="RDP status", enum=RDPStatus.values()))
    status: str = field(default="", metadata=dict(help_text="Status message"))


@dataclass
class VDISessionCreateStruct(BaseStruct):
    public_key: str = field(default=None, metadata=dict(help_text="Public key for WireGuard connection"))


@dataclass
class AllowedIPsStruct(BaseStruct):
    network: str = field(metadata=dict(help_text="network subnet"))
    nat: bool = field(metadata=dict(help_text="nat"), default=False)


@dataclass
class PeerStruct(BaseStruct):
    """Struct for peer information"""

    name: str = field(metadata=dict(help_text="Peer name"))
    public_key: str = field(metadata=dict(help_text="Public key"))
    endpoint: Optional[str] = field(metadata=dict(help_text="Connection endpoint to the peer"))
    allowed_ips: List[AllowedIPsStruct] = field(metadata=dict(help_text="Peer allowed subnets"))
    keep_alive: Optional[int] = field(metadata=dict(help_text="Persistence keep alive interval in seconds"))
    last_handshake: int = field(metadata=dict(help_text="Last handshake timestamp"), default=0)


@dataclass
class WireguardBaseStruct(BaseStruct):
    name: str = field(metadata=dict(help_text="Interface name"))
    address: str = field(metadata=dict(help_text="Interface address"))
    port: Optional[int] = field(metadata=dict(help_text="Port number"), default=None)
    mtu: Optional[int] = field(metadata=dict(help_text="mtu"), default=None)
    public_key: Optional[str] = field(metadata=dict(help_text="Interface public key"), default=None)


@dataclass
class WireguardInterfaceCreateStruct(WireguardBaseStruct):
    """Struct to posting a wireguard config"""

    private_key: Optional[str] = field(metadata=dict(help_text="Interface private key"), default=None)

    @property
    def encoded_interface_name(self):
        """Generating  interface name by encoding name and type"""
        return base64.b64encode(
            json.dumps({"name": self.name, "type": WgInterfaceType.WG.value}).encode("utf-8")
        ).decode("utf-8")

    @property
    def dict(self) -> dict:
        """Convert to dictionary"""
        result = {
            "name": self.name,
            "address": self.address,
            "port": self.port,
            "mtu": self.mtu,
            "public_key": self.public_key,
            "private_key": self.private_key,
        }
        return result

    swagger_types = {
        "name": "string",
        "address": "string",
        "port": "int",
        "peers": "list[PeerStruct]",
        "mtu": "int",
        "public_key": "string",
        "private_key": "string",
    }

    attribute_map = {
        "name": "name",
        "address": "address",
        "port": "port",
        "peers": "peers",
        "mtu": "mtu",
        "public_key": "public_key",
        "private_key": "private_key",
    }


@dataclass
class WireguardInterfaceStruct(WireguardBaseStruct):
    """Struct for listing wireguard configuration information"""

    type: str = field(metadata=dict(help_text="Connection type"), default="")
    interface_id: str = field(metadata=dict(help_text="Interface id"), default="")

    def decode_interface_name(self):
        """Decode interface name to extract name and type"""
        self.interface_id = self.name
        self.name, self.type = json.loads(base64.b64decode(self.name).decode("utf-8")).values()
        return


@dataclass
class WireguardConfigStruct(WireguardInterfaceCreateStruct):
    """WireGuard configuration document"""

    peers: Optional[List[PeerStruct]] = field(default=None, metadata=dict(help_text="List of peers"))


@dataclass
class VMConfigStruct(BaseStruct):
    """A virtual machine configuration"""

    memory: int = field(metadata=dict(help_text="Amount of memory in MB"))
    vcpus: int = field(metadata=dict(help_text="Number of virtual CPUs"))
    bootdisk: int = field(metadata=dict(help_text="Boot disk size in GB"))
    image_id: str = field(metadata=dict(help_text="Image ID for the VM"))
    cloud_init_template: str = field(metadata=dict(help_text="Cloud-init template name"))
    vgpu_profile: Optional[str] = field(default=None, metadata=dict(help_text="vGPU profile to use, e.g., 'nvidia-t4'"))


# --- Profile Structs ---


@dataclass
class VDIProfileCreateStruct(BaseStruct):
    """VDI Profile configuration"""

    name: str = field(metadata=dict(help_text="Name of the profile"))
    vm_config: VMConfigStruct = field(metadata=dict(help_text="VM configuration"))
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace to deploy into"))
    standby_pool_size: int = field(metadata=dict(help_text="Number of standby VMs"))
    instance_behavior: str = field(metadata=dict(help_text="Instance behavior", enum=InstanceBehaviorEnum.values()))
    recycle_time_seconds: int = field(metadata=dict(help_text="Time before recycling a VM"))
    availability: str = field(metadata=dict(help_text="Availability mode", enum=AvailabilityEnum.values()))
    service_account_id: str = field(metadata=dict(help_text="Service account ID for VDI profile authentication"))
    backup_policy_id: Optional[str] = field(default=None, metadata=dict(help_text="Optional backup policy"))
    wireguard_config: Optional[WireguardInterfaceCreateStruct] = field(
        default=None, metadata=dict(help_text="WireGuard server configuration")
    )
    granted_roles: Optional[List[str]] = field(default=None, metadata=dict(help_text="List of granted roles"))


@dataclass
class VDIStandbyVM(BaseStruct):
    """VDI Standby VM information"""

    vm_id: int = field(metadata=dict(help_text="VM ID"))
    vm_name: str = field(metadata=dict(help_text="VM name"))
    status: str = field(metadata=dict(help_text="VM status", enum=StandbyStatus.values()))
    rdp_status: str = field(
        metadata=dict(help_text="RDP status", default=RDPStatus.NOT_RUNNING.value, enum=RDPStatus.values())
    )
    rdp_host: Optional[str] = field(default=None, metadata=dict(help_text="RDP host"))


@dataclass
class VDIProfileStruct(BaseStruct):
    """VDI Profile configuration"""

    name: str = field(metadata=dict(help_text="Name of the profile"))
    vm_config: VMConfigStruct = field(metadata=dict(help_text="VM configuration"))
    cloudspace_id: str = field(metadata=dict(help_text="Cloudspace to deploy into"))
    standby_pool_size: int = field(metadata=dict(help_text="Number of standby VMs"))
    instance_behavior: str = field(metadata=dict(help_text="Instance behavior (single-use / dedicated)"))
    recycle_time_seconds: int = field(metadata=dict(help_text="Time before recycling a VM"))
    availability: str = field(metadata=dict(help_text="Availability mode"))
    profile_id: str = field(default="", metadata=dict(help_text="Generated profile ID"))
    created_at: int = field(default=0, metadata=dict(help_text="Creation timestamp"))
    updated_at: int = field(default=0, metadata=dict(help_text="Last update timestamp"))
    wireguard_id: str = field(default="", metadata=dict(help_text="WireGuard interface ID, if applicable"))
    backup_policy_id: Optional[str] = field(default=None, metadata=dict(help_text="Optional backup policy"))
    vms: List[VDIStandbyVM] = field(
        default_factory=list, metadata=dict(help_text="List of VMs associated with this profile")
    )
    status: str = field(default=None, metadata=dict(help_text="Profile status"))
    granted_roles: Optional[List[str]] = field(default=None, metadata=dict(help_text="List of granted roles"))


# --- Session/User/Connection Structs ---


@dataclass
class VDISessionConnectionStruct(BaseStruct):
    connected_at: int = field(metadata=dict(help_text="Timestamp of the connection"))
    remote_ip: Optional[str] = field(default=None, metadata=dict(help_text="Client's public IP address"))
    geo_coded_ip_city: Optional[str] = field(
        default=None, metadata=dict(help_text="City determined from IP geolocation")
    )


@dataclass
class VDIUserStruct(BaseStruct):
    username: Optional[str] = field(default=None, metadata=dict(help_text="Username of the session owner"))
    email: Optional[str] = field(default=None, metadata=dict(help_text="Email of the session owner"))


# --- Session Structs ---


@dataclass
class ListVDISessionsStruct(BaseStruct):
    session_id: str = field(default="", metadata=dict(help_text="UUID of the session"))
    customer_id: str = field(default="", metadata=dict(help_text="Customer ID"))
    profile_id: str = field(default="", metadata=dict(help_text="Associated profile ID"))
    status: str = field(default="", metadata=dict(help_text="Session status"))
    user: VDIUserStruct = field(default=VDIUserStruct(), metadata=dict(help_text="User information"))
    vm_id: str = field(default="", metadata=dict(help_text="Associated VM ID"))
    created_at: float = field(default=0.0, metadata=dict(help_text="Creation timestamp"))
    rdp_host: Optional[str] = field(default=None, metadata=dict(help_text="RDP host for the session"))


# --- Update Structs ---


@dataclass
class VDIProfileUpdateStruct(BaseStruct):
    """VDI Profile - writable fields for update"""

    vm_config: Optional[VMConfigStruct] = field(default=None, metadata=dict(help_text="VM configuration"))
    standby_pool_size: Optional[int] = field(default=None, metadata=dict(help_text="Number of standby VMs"))
    recycle_time_seconds: Optional[int] = field(default=None, metadata=dict(help_text="Time before recycling a VM"))
    availability: Optional[str] = field(default=None, metadata=dict(help_text="Availability mode"))
    backup_policy_id: Optional[int] = field(default=None, metadata=dict(help_text="Optional backup policy"))
    wireguard_config: Optional[WireguardConfigStruct] = field(
        default=None, metadata=dict(help_text="WireGuard server configuration")
    )
    granted_roles: Optional[List[str]] = field(default=None, metadata=dict(help_text="List of granted roles"))


@dataclass
class VDISessionUpdateStruct(BaseStruct):
    status: Optional[str] = field(default=None, metadata=dict(help_text="Update session status"))
    wireguard_peer: Optional[PeerStruct] = field(
        default=None, metadata=dict(help_text="Updated WireGuard peer configuration")
    )
    rdp_host: Optional[str] = field(default=None, metadata=dict(help_text="Updated RDP host"))
    user: Optional[VDIUserStruct] = field(default=None, metadata=dict(help_text="Update session user information"))
    connections: Optional[List[VDISessionConnectionStruct]] = field(
        default=None, metadata=dict(help_text="Update session connection history")
    )


@dataclass
class SessionPeerStruct(BaseStruct):
    public_key: str = field(default="", metadata=dict(help_text="Public key of the peer"))
    endpoint: str = field(default="", metadata=dict(help_text="Endpoint of the peer"))
    allowed_ips: str = field(default="", metadata=dict(help_text="Allowed IPs for the peer"))


@dataclass
class SessionWireguardStruct(BaseStruct):
    address: str = field(default="", metadata=dict(help_text="WireGuard interface address"))
    peers: List[SessionPeerStruct] = field(default_factory=list, metadata=dict(help_text="List of WireGuard peers"))
    mtu: int = field(default=1420, metadata=dict(help_text="MTU for WireGuard interface"))


@dataclass
class VDISessionStruct(BaseStruct):
    wireguard_config: SessionWireguardStruct = field(
        default=None, metadata=dict(help_text="WireGuard configuration for the session")
    )
    vm_id: int = field(default=None, metadata=dict(help_text="Associated VM ID"))
    created_at: float = field(default=0.0, metadata=dict(help_text="Creation timestamp"))
    rdp_target_ip: str = field(default=None, metadata=dict(help_text="RDP host for the session"))
    rdp_target_port: int = field(default=None, metadata=dict(help_text="RDP port for the session"))


@dataclass
class LogLine(BaseStruct):
    """Log Line Model"""

    level: str = field(metadata=dict(help_text="Indicates the severity of the log line"))
    timestamp: float = field(metadata=dict(help_text="Epoch timestamp when the log line was logged"))
    message: str = field(metadata=dict(help_text="Message of the log line"))


@dataclass
class VDIStandbyTransitionLogs(BaseStruct):
    """Transition metadata for VDI standby job"""

    transition_id: str = field(metadata=dict(help_text="Transition / Job id"))
    from_state: str = field(metadata=dict(help_text="Starting state of the transition", enum=StandbyStatus.values()))
    current_state: str = field(metadata=dict(help_text="Current state of the transition", enum=StandbyStatus.values()))
    final_state: str = field(metadata=dict(help_text="Final state of the transition", enum=StandbyStatus.values()))
    start_timestamp: float = field(metadata=dict(help_text="Starting timestamp for transition"))
    end_timestamp: float = field(metadata=dict(help_text="Ending timestamp for transition"))


@dataclass
class TransitionLogsStruct(BaseStruct):
    """Per-task logs (same shape as your example)"""

    title: str = field(metadata=dict(help_text="Transition title"))
    logs: List[LogLine] = field(metadata=dict(help_text="Transition Logs"))


@dataclass
class VDIWindowsAgentURLStruct(BaseStruct):
    """VDI Windows Agent downloadable link"""

    url: str = field(metadata=dict(help_text="Download URL for VDI agent installer"))


@dataclass
class VDISessionStatusStruct(BaseStruct):
    status: str = field(default="", metadata=dict(help_text="Session status"))
