# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, <PERSON><PERSON><PERSON><PERSON>UC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

from dataclasses import dataclass, field
from typing import List

from meneja.structs.dataclasses import BaseStruct


@dataclass
class ServiceAccountUsageStruct(BaseStruct):
    """Service account usage information"""

    resource_type: str = field(metadata=dict(help_text="Type of resource using the service account"))
    resource_id: str = field(metadata=dict(help_text="ID of the resource using the service account"))


@dataclass
class ServiceAccountCreateStruct(BaseStruct):
    """Service account creation payload"""

    name: str = field(metadata=dict(help_text="Service account name"))
    role_ids: List[str] = field(metadata=dict(help_text="List of role IDs to assign to the service account"))


@dataclass
class ServiceAccountUpdateStruct(BaseStruct):
    """Service account update payload"""

    name: str = field(default=None, metadata=dict(help_text="Service account name (optional)"))
    role_ids: List[str] = field(
        default=None, metadata=dict(help_text="List of role IDs to assign to the service account (optional)")
    )


@dataclass
class ServiceAccountStruct(BaseStruct):
    """Service account information"""

    id: str = field(metadata=dict(help_text="Service account ID"))
    name: str = field(metadata=dict(help_text="Service account name"))
    role_ids: List[str] = field(metadata=dict(help_text="List of role IDs that this service account belongs to"))
    technical_organization: str = field(metadata=dict(help_text="Technical IAM organization"))
    user_organization: str = field(metadata=dict(help_text="User IAM organization"))
    customer_id: str = field(metadata=dict(help_text="Customer ID"))
    created_at: int = field(default=0, metadata=dict(help_text="Creation timestamp"))
    in_use_by: List[ServiceAccountUsageStruct] = field(
        default_factory=list, metadata=dict(help_text="Resources currently using this service account")
    )
