# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, R<PERSON><PERSON>D<PERSON><PERSON>, <PERSON><PERSON>CLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import logging
import random
from typing import List

from werkzeug.exceptions import BadRequest

from meneja.business import get_vco_iam_client
from meneja.lib.clients.g8.lib.rest import ApiException
from meneja.lib.itsyouonline import Access<PERSON>eni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ItsyouOnlineClient

logger = logging.getLogger(__name__)


def _get_service_account_api_key(customer_id: str, service_account_id: str, iam_vco_client: ItsyouOnlineClient) -> str:
    """
    Generate an API key for a service account

    Args:
        customer_id (str): Customer ID
        service_account_id (str): Service account ID
        iam_vco_client (ItsyouOnlineClient): IAM VCO client

    Returns:
        str: Generated API key secret
    """
    try:
        from meneja.model.vco.customer import Customer

        # Get service account to retrieve its technical organization
        db_service_account = Customer.get_service_account(customer_id, service_account_id)
        tech_org = db_service_account.tech_iam_organization

        logger.info("Generating API key for service account %s with organization: %s", service_account_id, tech_org)

        # Generate random label for the API key
        label = f"api_key_{service_account_id}_{random.randint(0, 100000)}"
        api_key_secret = iam_vco_client.generate_api_key(organization=tech_org, label=label, grant_type=True)

        logger.info("Successfully generated API key for service account %s", service_account_id)
        return api_key_secret

    except Exception as e:
        logger.error("Failed to generate API key for service account %s: %s", service_account_id, e)
        raise


def get_service_account_jwt(vco_id: str, customer_id: str, service_account_id: str) -> str:
    """
    Get JWT token using service account API key

    Args:
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID

    Returns:
        str: JWT token
    """
    try:
        from meneja.model.vco.customer import Customer

        iam_vco_client = get_vco_iam_client(vco_id)
        api_key_secret = _get_service_account_api_key(customer_id, service_account_id, iam_vco_client)

        # Get service account to retrieve its technical organization
        db_service_account = Customer.get_service_account(customer_id, service_account_id)
        tech_org = db_service_account.tech_iam_organization

        logger.info(
            "Getting JWT token using API key for service account %s with organization: %s",
            service_account_id,
            tech_org,
        )

        iam_vco_client.set_custom_jwt_from_api_key(client_id=tech_org, client_secret=api_key_secret)
        service_jwt = iam_vco_client.jwt

        logger.info("Successfully obtained JWT token for service account %s", service_account_id)
        return service_jwt

    except Exception as e:
        logger.error("Failed to get JWT from API key for service account %s: %s", service_account_id, e)
        raise BadRequest(f"Failed to get JWT from API key for service account {service_account_id}: {e}") from e


def get_service_account_jwt_with_scopes(
    vco_id: str, customer_id: str, service_account_id: str, required_scopes: List[str]
) -> str:
    """
    Get JWT token for service account with additional scopes if needed

    Args:
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID
        required_scopes (List[str]): List of required scopes

    Returns:
        str: JWT token with required scopes
    """
    try:
        from meneja.model.vco.customer import Customer

        iam_vco_client = get_vco_iam_client(vco_id)
        api_key_secret = _get_service_account_api_key(customer_id, service_account_id, iam_vco_client)

        # Get service account to retrieve its technical organization
        db_service_account = Customer.get_service_account(customer_id, service_account_id)
        tech_org = db_service_account.tech_iam_organization

        logger.info(
            "Getting JWT token with scopes %s for service account %s with organization: %s",
            required_scopes,
            service_account_id,
            tech_org,
        )

        # Request JWT with specific scopes
        iam_vco_client.set_custom_jwt_from_api_key(
            client_id=tech_org,
            client_secret=api_key_secret,
            scopes=",".join(required_scopes) if required_scopes else None
        )
        service_jwt = iam_vco_client.jwt

        logger.info("Successfully obtained JWT token with scopes for service account %s", service_account_id)
        return service_jwt

    except Exception as e:
        logger.error("Failed to get JWT with scopes for service account %s: %s", service_account_id, e)
        raise BadRequest(f"Failed to get JWT with scopes for service account {service_account_id}: {e}") from e


def handle_service_account_authorization_error(
    vco_id: str, customer_id: str, service_account_id: str, error: ApiException
) -> List[str]:
    """
    Handle G8 API authorization errors for service accounts and determine required scopes

    Args:
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID
        error (ApiException): The G8 API exception

    Returns:
        List[str]: List of required scopes needed for the operation

    Raises:
        AccessDeniedWithoutScope: When additional scopes are needed
    """
    # Check if this is an authorization error (401 from G8, should be 403)
    if error.status not in [401, 403]:
        raise error

    try:
        from meneja.model.vco.customer import Customer

        # Get service account and its roles to determine available scopes
        db_service_account = Customer.get_service_account(customer_id, service_account_id)
        customer = Customer.get_by_id(customer_id)

        # Get all roles assigned to this service account
        assigned_roles = []
        for role_id in db_service_account.role_ids:
            try:
                role = customer.get_role(role_id)
                assigned_roles.append(role)
            except Exception as e:
                logger.warning("Could not find role %s for service account %s: %s", role_id, service_account_id, e)
                continue

        # Determine required scopes based on the service account's roles
        # For G8 operations, we typically need admin scopes for the customer organization
        required_scopes = []

        # Add basic user scopes for the service account's technical organization
        tech_org = db_service_account.tech_iam_organization
        required_scopes.extend([
            f"user:memberof:{tech_org}",
            f"user:memberof:{customer.technical_organization}",
        ])

        # Add admin scopes if the service account has admin roles
        for role in assigned_roles:
            if role.permissions.execute:  # If role has execute permission, add admin scopes
                required_scopes.extend([
                    f"user:memberof:{customer.technical_organization}.admin",
                    f"user:memberof:{customer.organization}.admin",
                ])
                break

        logger.info(
            "Service account %s needs additional scopes for G8 operation: %s",
            service_account_id,
            required_scopes
        )

        # Raise AccessDeniedWithoutScope to trigger the scope request flow
        raise AccessDeniedWithoutScope(
            f"Service account {service_account_id} needs additional scopes for this operation",
            *required_scopes
        )

    except AccessDeniedWithoutScope:
        raise
    except Exception as e:
        logger.error("Failed to handle service account authorization error: %s", e)
        raise error  # Re-raise original error if we can't handle it


def retry_with_service_account_scopes(operation_func, vco_id: str, customer_id: str, service_account_id: str, **kwargs):
    """
    Retry an operation with updated service account JWT if authorization fails

    Args:
        operation_func: The function to execute that may need additional scopes
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID
        **kwargs: Keyword arguments to pass to the operation function (must include 'jwt')

    Returns:
        The result of the operation function

    Raises:
        The original exception if retry fails or if it's not an authorization error
    """
    if 'jwt' not in kwargs:
        raise ValueError("JWT parameter is required in kwargs for service account operations")

    try:
        # First attempt with current JWT
        return operation_func(**kwargs)

    except ApiException as e:
        # Check if this is an authorization error that we can handle
        if e.status not in [401, 403]:
            raise

        logger.info(
            "Service account %s got authorization error (%s), attempting to get additional scopes",
            service_account_id,
            e.status
        )

        try:
            # Handle the authorization error and get required scopes
            handle_service_account_authorization_error(
                vco_id, customer_id, service_account_id, e
            )

        except AccessDeniedWithoutScope as scope_error:
            # Get new JWT with required scopes
            logger.info(
                "Requesting new JWT with scopes %s for service account %s",
                scope_error.scopes,
                service_account_id
            )

            new_jwt = get_service_account_jwt_with_scopes(
                vco_id, customer_id, service_account_id, list(scope_error.scopes)
            )

            # Update the JWT parameter
            kwargs['jwt'] = new_jwt

            # Retry the operation with new JWT
            logger.info("Retrying operation with updated JWT for service account %s", service_account_id)
            return operation_func(**kwargs)

        except Exception as handle_error:
            logger.error("Failed to handle authorization error for service account %s: %s", service_account_id, handle_error)
            raise e  # Re-raise original error


def with_service_account_scope_retry(vco_id: str, customer_id: str, service_account_id: str):
    """
    Decorator to automatically retry operations with updated service account JWT on authorization failures

    Args:
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID

    Returns:
        Decorator function
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            return retry_with_service_account_scopes(
                func, vco_id, customer_id, service_account_id, *args, **kwargs
            )
        return wrapper
    return decorator
