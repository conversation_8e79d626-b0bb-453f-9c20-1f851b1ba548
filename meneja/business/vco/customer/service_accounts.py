# -*- coding: utf-8 -*-
# COPYRIGHT (C) 2018-2021 GIG.TECH NV
# ALL RIGHTS RESERVED.
#
# ALTHOUGH YOU MAY BE ABLE TO READ THE CONTENT OF THIS FILE, THIS FILE
# CONTAINS CONFIDENTIAL INFORMATION OF GIG.TECH NV. YOU ARE NOT ALLOWED
# TO MODIFY, REPRODUC<PERSON>, DISCLOSE, PUBLISH OR DISTRIBUTE ITS CONTENT,
# EMBED IT IN OTHER SOFTWARE, OR CREATE DERIVATIVE WORKS, UNLESS PRIOR
# WRITTEN PERMISSION IS OBTAINED FROM GIG.TECH NV.
#
# THE COPYRIGHT NOTICE ABOVE DOES NOT EVIDENCE ANY ACTUAL OR INTENDED
# PUBLICATION OF SUCH SOURCE CODE.
#
# @@license_version:1.9@@

import logging
import time
from typing import Iterable, List

from werkzeug.exceptions import BadRequest, NotFound

from meneja.business import get_vco_iam_client
from meneja.lib.itsyouonline import ItsyouOnlineClient, OrganizationAlreadyMember, OrganizationNotFound
from meneja.lib.utils import create_new_identifier
from meneja.model.vco.customer import Customer, ServiceAccount, ServiceAccountUsage
from meneja.structs.vco.dataclasses.service_account import ServiceAccountStruct, ServiceAccountUsageStruct

logger = logging.getLogger(__name__)


def _org_exists(tree, org):
    """Check if organization exists in the tree structure"""
    if tree["globalid"] == org:
        return True
    if "children" not in tree:
        return False
    for child in tree["children"]:
        if _org_exists(child, org):
            return True
    return False


def _get_service_account_from_customer(customer: Customer, service_account_id: str) -> ServiceAccount:
    """
    Get service account from customer object to ensure changes persist when customer.save() is called

    Args:
        customer (Customer): Customer object from database
        service_account_id (str): Service account ID to find

    Returns:
        ServiceAccount: Service account object attached to customer

    Raises:
        NotFound: If service account is not found
    """
    for sa in customer.service_accounts:
        if sa.service_account_id == service_account_id:
            return sa
    raise NotFound(f"Service account {service_account_id} not found")


def _db_to_struct(db_service_account, customer_id: str) -> ServiceAccountStruct:
    """Convert database ServiceAccount model to ServiceAccountStruct"""
    in_use_by = []
    if db_service_account.in_use_by:
        in_use_by = [
            ServiceAccountUsageStruct(
                resource_type=usage.resource_type,
                resource_id=usage.resource_id,
            )
            for usage in db_service_account.in_use_by
        ]

    return ServiceAccountStruct(
        id=db_service_account.service_account_id,
        name=db_service_account.name,
        role_ids=db_service_account.role_ids,
        technical_organization=db_service_account.tech_iam_organization,
        user_organization=db_service_account.iam_organization,
        customer_id=customer_id,
        created_at=db_service_account.created_at,
        in_use_by=in_use_by,
    )


def _validate_service_account_name(name: str) -> str:
    """Validate and normalize service account name"""
    if not name or not name.strip():
        raise BadRequest("Service account name cannot be empty")

    name = name.strip()
    if len(name) < 2:
        raise BadRequest("Service account name must be at least 2 characters long")
    if len(name) > 50:
        raise BadRequest("Service account name must be less than 50 characters")

    return name


def _validate_role_ids(role_ids: List[str]) -> None:
    """Validate role IDs list"""
    if not role_ids or len(role_ids) == 0:
        raise BadRequest("At least one role_id must be specified")


def _add_service_account_to_roles(iam_vco_client, iam_user_client, roles, tech_org: str, user_org: str) -> None:
    """Add service account organizations to multiple roles"""
    for role in roles:
        logger.info(
            "Adding service account technical org %s as member to role %s", tech_org, role.tech_iam_organization
        )
        try:
            iam_vco_client.add_orgmember(role.tech_iam_organization, tech_org)
        except OrganizationAlreadyMember:
            logger.warning(
                "Service account technical org %s is already a member of role %s", tech_org, role.tech_iam_organization
            )
        except Exception as e:
            logger.error("Failed to add service account tech org to role %s: %s", role.tech_iam_organization, e)
            raise

        logger.info("Adding service account user org %s as member to role %s", user_org, role.iam_organization)
        try:
            iam_user_client.add_orgmember(role.iam_organization, user_org)
        except OrganizationAlreadyMember:
            logger.warning(
                "Service account user org %s is already a member of role %s", user_org, role.iam_organization
            )
        except Exception as e:
            logger.error("Failed to add service account user org to role %s: %s", role.iam_organization, e)
            raise


def _remove_service_account_from_roles(iam_vco_client, iam_user_client, roles, tech_org: str, user_org: str) -> None:
    """Remove service account organizations from multiple roles"""
    for role in roles:
        logger.info("Removing service account from role %s", role.tech_iam_organization)
        try:
            iam_vco_client.delete_orgmember(role.tech_iam_organization, tech_org)
            iam_user_client.delete_orgmember(role.iam_organization, user_org)
        except OrganizationNotFound:
            logger.warning(
                "Organization not found when removing from role %s, may have been already deleted", role.role_id
            )
        except Exception as e:
            logger.error("Failed to remove from role %s: %s", role.role_id, e)
            raise


def list_service_accounts(customer_id: str) -> Iterable[ServiceAccountStruct]:
    """
    List customer service accounts

    Args:
        customer_id (str): Customer ID

    Returns:
        Iterable[ServiceAccountStruct]: List of service accounts
    """
    db_service_accounts = Customer.list_service_accounts(customer_id)
    return [_db_to_struct(db_sa, customer_id) for db_sa in db_service_accounts]


def get_service_account(customer_id: str, service_account_id: str) -> ServiceAccountStruct:
    """
    Get service account by ID

    Args:
        customer_id (str): Customer ID
        service_account_id (str): Service account ID

    Returns:
        ServiceAccountStruct: Service account details
    """
    try:
        db_service_account = Customer.get_service_account(customer_id, service_account_id)
        return _db_to_struct(db_service_account, customer_id)
    except Exception as e:
        logger.error(
            "Unexpected error getting service account %s for customer %s: %s", service_account_id, customer_id, e
        )
        raise


def create_service_account(
    jwt: str, vco_id: str, customer_id: str, name: str, role_ids: List[str]
) -> ServiceAccountStruct:
    """
    Create customer service account

    Args:
        jwt (str): Customer admin JWT Token
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        name (str): Service account name
        role_ids (List[str]): Role IDs to assign service account to

    Returns:
        ServiceAccountStruct
    """
    # Validate input
    name = _validate_service_account_name(name)
    _validate_role_ids(role_ids)

    # Get customer and validate all roles exist
    customer = Customer.get_by_id(customer_id=customer_id)
    roles = [Customer.get_role(customer_id, role_id) for role_id in role_ids]

    # Get IAM clients
    iam_vco_client = get_vco_iam_client(vco_id)
    iam_user_client = ItsyouOnlineClient(jwt=jwt)

    # Get organization trees to check if service_accounts org exists and for unique ID generation
    user_tree = iam_user_client.get_tree(f"{customer.organization}")
    tech_tree = iam_vco_client.get_tree(f"{customer.technical_organization}")

    # Create service_accounts sub organization if necessary
    if not _org_exists(user_tree, f"{customer.organization}.service_accounts"):
        logger.info("Creating service_accounts organization for user org: %s", customer.organization)
        iam_user_client.create_suborganization(f"{customer.organization}.service_accounts")

    if not _org_exists(tech_tree, f"{customer.technical_organization}.service_accounts"):
        logger.info("Creating service_accounts organization for tech org: %s", customer.technical_organization)
        iam_vco_client.create_suborganization(f"{customer.technical_organization}.service_accounts")

    # Create a new unique service_account_id
    def service_account_id_exists(service_account_id):
        if _org_exists(
            tech_tree, f"{customer.technical_organization}.service_accounts.{service_account_id}"
        ) or _org_exists(user_tree, f"{customer.organization}.service_accounts.{service_account_id}"):
            return True
        return False

    service_account_id = create_new_identifier(name, service_account_id_exists)

    # Create the service account in IAM
    tech_org = f"{customer.technical_organization}.service_accounts.{service_account_id}"
    user_org = f"{customer.organization}.service_accounts.{service_account_id}"

    logger.info("Creating service account organizations: %s, %s", tech_org, user_org)

    iam_vco_client.create_suborganization(tech_org)
    iam_user_client.create_suborganization(user_org)
    iam_vco_client.add_orgmember(tech_org, user_org)

    # Add service account organizations as members to all specified roles
    _add_service_account_to_roles(iam_vco_client, iam_user_client, roles, tech_org, user_org)

    # Create the service account in the customer database
    db_service_account = ServiceAccount(
        service_account_id=service_account_id,
        name=name,
        role_ids=role_ids,
        iam_organization=user_org,
        tech_iam_organization=tech_org,
        created_at=int(time.time()),
    )
    customer.service_accounts.append(db_service_account)
    customer.save()

    return _db_to_struct(db_service_account, customer_id)


def delete_service_account(vco_id: str, customer_id: str, service_account_id: str) -> None:
    """
    Delete service account

    Args:
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID
    """
    # Get customer and service account from database
    customer = Customer.get_by_id(customer_id=customer_id)

    try:
        db_service_account = Customer.get_service_account(customer_id, service_account_id)
    except Exception as exc:
        raise NotFound(f"Service account '{service_account_id}' not found") from exc

    # Check if service account is currently in use
    if db_service_account.in_use_by:
        resource_list = [
            f"{usage.resource_type} resource '{usage.resource_id}'" for usage in db_service_account.in_use_by
        ]
        raise BadRequest(
            f"Cannot delete service account '{service_account_id}'. "
            f"It is currently in use by {len(db_service_account.in_use_by)} "
            f"resource(s): {', '.join(resource_list)}. "
            "Please detach it from all resources before deleting."
        )

    # Get IAM clients
    iam_vco_client = get_vco_iam_client(vco_id)
    # iam_user_client = ItsyouOnlineClient(jwt=jwt)

    # Get organization names from database record
    tech_org = db_service_account.tech_iam_organization
    user_org = db_service_account.iam_organization

    logger.info("Deleting service account organizations: %s, %s", tech_org, user_org)

    # Delete the organizations from IAM
    try:
        iam_vco_client.delete_organization(name=tech_org)
        logger.info("Successfully deleted technical organization: %s", tech_org)
        # iam_user_client.delete_organization(name=user_org)
        # logger.info("Successfully deleted user organization: %s", user_org)
    except OrganizationNotFound:
        logger.warning("Organizations not found in IAM, may have been already deleted")
    except Exception as e:
        logger.error("Failed to delete organizations: %s", e)
        raise

    # Remove service account from database
    customer.service_accounts = [sa for sa in customer.service_accounts if sa.service_account_id != service_account_id]
    customer.save()
    logger.info("Successfully deleted service account %s from database", service_account_id)


def update_service_account(
    jwt: str, vco_id: str, customer_id: str, service_account_id: str, name: str = None, role_ids: List[str] = None
) -> ServiceAccountStruct:
    """
    Update service account name and/or roles

    Args:
        jwt (str): JWT
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID
        name (str, optional): New service account name
        role_ids (List[str], optional): New role IDs

    Returns:
        ServiceAccountStruct: Updated service account
    """
    # Get customer and service account from database
    customer = Customer.get_by_id(customer_id=customer_id)

    try:
        db_service_account = _get_service_account_from_customer(customer, service_account_id)
    except Exception as e:
        logger.error("Unexpected error updating service account %s: %s", service_account_id, e)
        raise

    # Update name if provided
    if name:
        db_service_account.name = _validate_service_account_name(name)

    # Update roles if provided
    if role_ids is not None:
        _validate_role_ids(role_ids)
        # Validate all new roles exist
        for role_id in role_ids:
            Customer.get_role(customer_id, role_id)

        # Calculate role differences
        current_role_ids = set(db_service_account.role_ids)
        new_role_ids = set(role_ids)

        roles_to_remove_ids = current_role_ids - new_role_ids
        roles_to_add_ids = new_role_ids - current_role_ids

        # Get IAM clients
        iam_vco_client = get_vco_iam_client(vco_id)
        iam_user_client = ItsyouOnlineClient(jwt=jwt)

        # Remove from roles that are no longer assigned
        if roles_to_remove_ids:
            roles_to_remove = [Customer.get_role(customer_id, role_id) for role_id in roles_to_remove_ids]
            _remove_service_account_from_roles(
                iam_vco_client,
                iam_user_client,
                roles_to_remove,
                db_service_account.tech_iam_organization,
                db_service_account.iam_organization,
            )

        # Add to new roles
        if roles_to_add_ids:
            roles_to_add = [Customer.get_role(customer_id, role_id) for role_id in roles_to_add_ids]
            _add_service_account_to_roles(
                iam_vco_client,
                iam_user_client,
                roles_to_add,
                db_service_account.tech_iam_organization,
                db_service_account.iam_organization,
            )

        # Update role_ids in database
        db_service_account.role_ids = role_ids

    # Save changes to database
    customer.save()
    logger.info("Successfully updated service account %s", service_account_id)

    return _db_to_struct(db_service_account, customer_id)


def add_role_to_service_account(
    jwt: str, vco_id: str, customer_id: str, service_account_id: str, role_id: str
) -> ServiceAccountStruct:
    """
    Add a role to an existing service account

    Args:
        jwt (str): JWT
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID
        role_id (str): Role ID to add

    Returns:
        ServiceAccountStruct: Updated service account
    """
    try:
        # Get customer, service account, and role
        customer = Customer.get_by_id(customer_id=customer_id)
        db_service_account = _get_service_account_from_customer(customer, service_account_id)
        role = Customer.get_role(customer_id, role_id)

        # Check if role is already assigned
        if role_id in db_service_account.role_ids:
            raise BadRequest(f"Role '{role_id}' is already assigned to service account '{service_account_id}'")

        # Get IAM clients
        iam_vco_client = get_vco_iam_client(vco_id)
        iam_user_client = ItsyouOnlineClient(jwt=jwt)

        # Add service account to role organizations
        _add_service_account_to_roles(
            iam_vco_client,
            iam_user_client,
            [role],
            db_service_account.tech_iam_organization,
            db_service_account.iam_organization,
        )

        # Update database
        db_service_account.role_ids.append(role_id)
        customer.save()

        return _db_to_struct(db_service_account, customer_id)
    except Exception as e:
        logger.error("Unexpected error adding role %s to service account %s: %s", role_id, service_account_id, e)
        raise


def remove_role_from_service_account(
    jwt: str, vco_id: str, customer_id: str, service_account_id: str, role_id: str
) -> ServiceAccountStruct:
    """
    Remove a role from an existing service account

    Args:
        jwt (str): JWT
        vco_id (str): VCO ID
        customer_id (str): Customer ID
        service_account_id (str): Service account ID
        role_id (str): Role ID to remove

    Returns:
        ServiceAccountStruct: Updated service account
    """
    try:
        # Get customer, service account, and role
        customer = Customer.get_by_id(customer_id=customer_id)
        db_service_account = _get_service_account_from_customer(customer, service_account_id)
        role = Customer.get_role(customer_id, role_id)

        # Check if role is assigned
        if role_id not in db_service_account.role_ids:
            raise BadRequest(f"Role '{role_id}' is not assigned to service account '{service_account_id}'")

        # Get IAM clients
        iam_vco_client = get_vco_iam_client(vco_id)
        iam_user_client = ItsyouOnlineClient(jwt=jwt)

        # Remove service account from role organizations
        tech_org = db_service_account.tech_iam_organization
        user_org = db_service_account.iam_organization

        logger.info("Removing service account %s from role %s", tech_org, role.tech_iam_organization)
        try:
            iam_vco_client.delete_orgmember(role.tech_iam_organization, tech_org)
        except OrganizationNotFound:
            logger.warning(
                "Technical organization %s not found, may have been already deleted", role.tech_iam_organization
            )

        logger.info("Removing service account %s from role %s", user_org, role.iam_organization)
        try:
            iam_user_client.delete_orgmember(role.iam_organization, user_org)
        except OrganizationNotFound:
            logger.warning("User organization %s not found, may have been already deleted", role.iam_organization)

        # Update database
        db_service_account.role_ids.remove(role_id)
        customer.save()

        return ServiceAccountStruct(
            id=db_service_account.service_account_id,
            name=db_service_account.name,
            role_ids=db_service_account.role_ids,
            technical_organization=db_service_account.tech_iam_organization,
            user_organization=db_service_account.iam_organization,
            customer_id=customer_id,
            created_at=db_service_account.created_at,
        )
    except Exception as e:
        logger.error("Unexpected error removing role %s from service account %s: %s", role_id, service_account_id, e)
        raise


def get_service_accounts_by_role(customer_id: str, role_id: str) -> Iterable[ServiceAccountStruct]:
    """
    Get all service accounts that have the specified role

    Args:
        customer_id (str): Customer ID
        role_id (str): Role ID

    Returns:
        Iterable[ServiceAccountStruct]: List of service accounts with the specified role
    """
    try:
        # Validate role exists
        Customer.get_role(customer_id, role_id)

        # Get service accounts with this role
        db_service_accounts = Customer.get_service_accounts_by_role(customer_id, role_id)
        return [_db_to_struct(db_sa, customer_id) for db_sa in db_service_accounts]
    except Exception as e:
        logger.error("Unexpected error getting service accounts for role %s: %s", role_id, e)
        raise


def attach_service_account_to_resource(
    customer_id: str, resource_type: str, resource_id: str, service_account_id: str
) -> ServiceAccountStruct:
    """
    Attach a service account to a resource

    Args:
        customer_id (str): Customer ID
        resource_type (str): Type of resource (VDI, etc.)
        resource_id (str): ID of the resource
        service_account_id (str): Service account ID to attach

    Returns:
        ServiceAccountStruct: Updated service account
    """
    try:
        # Get customer and service account
        customer = Customer.get_by_id(customer_id=customer_id)
        db_service_account = _get_service_account_from_customer(customer, service_account_id)

        # Check if service account is attached to this specific resource
        for usage in db_service_account.in_use_by:
            if usage.resource_type == resource_type and usage.resource_id == resource_id:
                raise BadRequest(
                    f"Service account '{service_account_id}' is already attached to "
                    f"{resource_type} resource '{resource_id}'"
                )

        # Add new usage to the list
        new_usage = ServiceAccountUsage(resource_type=resource_type, resource_id=resource_id)
        db_service_account.in_use_by.append(new_usage)
        customer.save()

        logger.info(
            "Successfully attached service account %s to %s resource %s",
            service_account_id,
            resource_type,
            resource_id,
        )

    except Exception as e:
        logger.error(
            "Error attaching service account %s to %s resource %s: %s",
            service_account_id,
            resource_type,
            resource_id,
            e,
        )
        raise


def update_service_account_resources(
    customer_id: str, resource_type: str, resource_id: str, old_service_account_id: str, new_service_account_id: str
) -> ServiceAccountStruct:
    """
    Update service account resources by moving a resource from old service account to new service account

    Args:
        customer_id (str): Customer ID
        resource_type (str): Type of resource (VDI, etc.)
        resource_id (str): ID of the resource
        old_service_account_id (str): Current service account ID
        new_service_account_id (str): New service account ID to move resource to

    Returns:
        ServiceAccountStruct: Updated new service account
    """
    try:
        # Get customer and new service account
        customer = Customer.get_by_id(customer_id=customer_id)
        new_service_account = _get_service_account_from_customer(customer, new_service_account_id)

        # Try to get old service account, but continue if it doesn't exist
        old_service_account = None
        try:
            old_service_account = _get_service_account_from_customer(customer, old_service_account_id)
        except NotFound:
            logger.warning("Old service account '%s' not found, proceeding with attachment", old_service_account_id)

        # If old service account exists, remove the resource from it
        if old_service_account:
            usage_to_remove = None
            for usage in old_service_account.in_use_by:
                if usage.resource_type == resource_type and usage.resource_id == resource_id:
                    usage_to_remove = usage
                    break

            if usage_to_remove:
                old_service_account.in_use_by.remove(usage_to_remove)

        # Check if resource is already attached to new service account
        for usage in new_service_account.in_use_by:
            if usage.resource_type == resource_type and usage.resource_id == resource_id:
                raise BadRequest(
                    f"Resource {resource_type}:{resource_id} is already "
                    f"attached to service account '{new_service_account_id}'"
                )

        # Add resource to new service account
        new_usage = ServiceAccountUsage(resource_type=resource_type, resource_id=resource_id)
        new_service_account.in_use_by.append(new_usage)

        # Save changes
        customer.save()

        logger.info(
            "Successfully moved %s resource '%s' from service account '%s' to '%s'",
            resource_type,
            resource_id,
            old_service_account_id,
            new_service_account_id,
        )

        return _db_to_struct(new_service_account, customer_id)

    except Exception as e:
        logger.error("Error updating service account resources for %s:%s: %s", resource_type, resource_id, e)
        raise
